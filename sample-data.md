# 🏥 Sample Data for Clinic Portfolio Screenshots

## 👥 Realistic Patients (Co<PERSON> & Paste into Forms)

### Patient 1
- **ID**: 3
- **Nome**: <PERSON>
- **Email**: <EMAIL>
- **Género**: Feminino
- **Data de Nascimento**: 1952-03-15
- **Telemóvel**: 912345678
- **NIF**: 123456789
- **Estado**: Reformado
- **Tipo de Atendimento**: Domicilio
- **Morada**: <PERSON><PERSON>, 15
- **Cidade**: Sacavém
- **Código Postal**: 2685-123
- **Observações**: Hipertensão arterial controlada

### Patient 2
- **ID**: 4
- **Nome**: <PERSON>
- **Email**: <EMAIL>
- **Género**: Masculino
- **Data de Nascimento**: 1956-07-22
- **Telemóvel**: 913456789
- **NIF**: 234567890
- **Estado**: Reformado
- **Tipo de Atendimento**: Clinica
- **<PERSON>rada**: <PERSON><PERSON>. <PERSON>ú<PERSON>, 42
- **Cidade**: <PERSON>res
- **Código Postal**: 2670-456
- **Observações**: Diabetes tipo 2

### Patient 3
- **ID**: 5
- **Nome**: Ana Beatriz Ferreira
- **Email**: <EMAIL>
- **Género**: Feminino
- **Data de Nascimento**: 1979-11-08
- **Telemóvel**: 914567890
- **NIF**: 345678901
- **Estado**: Ativo
- **Tipo de Atendimento**: Clinica
- **Morada**: Rua do Comércio, 8
- **Cidade**: Sacavém
- **Código Postal**: 2685-789
- **Observações**: Alergia à penicilina

### Patient 4
- **ID**: 6
- **Nome**: Carlos Alberto Pereira
- **Email**: <EMAIL>
- **Género**: Masculino
- **Data de Nascimento**: 1966-04-12
- **Telemóvel**: 915678901
- **NIF**: 456789012
- **Estado**: Ativo
- **Tipo de Atendimento**: Domicilio
- **Morada**: Largo da Igreja, 3
- **Cidade**: Moscavide
- **Código Postal**: 2685-012
- **Observações**: Problemas cardíacos

### Patient 5
- **ID**: 7
- **Nome**: Isabel Maria Rodrigues
- **Email**: <EMAIL>
- **Género**: Feminino
- **Data de Nascimento**: 1961-09-30
- **Telemóvel**: 916789012
- **NIF**: 567890123
- **Estado**: Reformado
- **Tipo de Atendimento**: Clinica
- **Morada**: Rua Nova, 27
- **Cidade**: Sacavém
- **Código Postal**: 2685-345
- **Observações**: Artrite reumatoide

## 💊 Realistic Medications

### Medication 1
- **ID**: 5
- **Nome**: Paracetamol
- **Stock**: 150
- **Tipo**: Analgésico
- **Dosagem**: 500mg
- **Preço**: 3.50
- **Fornecedor**: Farmácia Central

### Medication 2
- **ID**: 6
- **Nome**: Ibuprofeno
- **Stock**: 120
- **Tipo**: Anti-inflamatório
- **Dosagem**: 400mg
- **Preço**: 4.20
- **Fornecedor**: Medifarm

### Medication 3
- **ID**: 7
- **Nome**: Omeprazol
- **Stock**: 80
- **Tipo**: Protetor gástrico
- **Dosagem**: 20mg
- **Preço**: 8.90
- **Fornecedor**: Farmácia Central

### Medication 4
- **ID**: 8
- **Nome**: Losartan
- **Stock**: 95
- **Tipo**: Anti-hipertensor
- **Dosagem**: 50mg
- **Preço**: 12.30
- **Fornecedor**: Medifarm

### Medication 5
- **ID**: 9
- **Nome**: Metformina
- **Stock**: 110
- **Tipo**: Antidiabético
- **Dosagem**: 850mg
- **Preço**: 6.75
- **Fornecedor**: Farmácia São João

### Medication 6
- **ID**: 10
- **Nome**: Sinvastatina
- **Stock**: 75
- **Tipo**: Hipolipemiante
- **Dosagem**: 20mg
- **Preço**: 9.40
- **Fornecedor**: Farmácia Central

## 📅 Sample Bookings to Create

### Today's Appointments
1. **Maria Silva Santos** + **Losartan** - Domicilio - 09:00-09:30
2. **Ana Beatriz Ferreira** + **Ibuprofeno** - Clinica - 10:30-11:00
3. **Isabel Maria Rodrigues** + **Sinvastatina** - Clinica - 14:00-14:30

### Tomorrow's Appointments
1. **João Manuel Costa** + **Metformina** - Clinica - 08:30-09:00
2. **Carlos Alberto Pereira** + **Paracetamol** - Domicilio - 11:00-11:30

### Next Week
1. **Isabel Maria Rodrigues** + **Omeprazol** - Clinica - 10:00-10:30
2. **Carlos Alberto Pereira** + **Losartan** - Domicilio - 14:30-15:00

---

## 🎯 Portfolio Screenshot Strategy

### Best Views to Capture:
1. **Admin Dashboard** - Shows overview with populated stats
2. **Patient List** - Shows realistic patient names and data
3. **Calendar View** - Shows appointments spread across days
4. **Patient Detail Page** - Shows individual patient with bookings
5. **Medication List** - Shows realistic medication inventory
6. **Booking Form** - Shows the booking creation process
7. **Mobile View** - Shows responsive design

### Tips for Great Screenshots:
- Use realistic Portuguese names and addresses
- Show a mix of "Clinica" and "Domicilio" appointments
- Include both "Ativo" and "Reformado" patients
- Show realistic medication names and prices
- Capture different time slots to show busy clinic
- Show the calendar with multiple appointments
