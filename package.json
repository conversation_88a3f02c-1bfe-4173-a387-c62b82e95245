{"name": "clinic", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "build:analyze": "ANALYZE=true npm run build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "postinstall": "prisma generate", "prebuild": "npm run lint", "postbuild": "node scripts/check-build-size.js", "type-check": "tsc --noEmit", "clean": "rmdir /s /q .next 2>nul & rmdir /s /q out 2>nul & rmdir /s /q node_modules\\.cache 2>nul & echo Clean completed", "deploy:check": "npm run build && npm run type-check"}, "dependencies": {"@clerk/elements": "^0.22.20", "@clerk/express": "^1.3.46", "@clerk/nextjs": "^6.11.0", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.2.1", "@schedule-x/current-time": "^2.18.0", "@schedule-x/event-modal": "^2.18.0", "@schedule-x/events-service": "^2.18.0", "@schedule-x/react": "^2.18.0", "@schedule-x/scroll-controller": "^2.18.0", "@schedule-x/theme-default": "^2.18.0", "@schedule-x/theme-shadcn": "^2.18.0", "@types/react-big-calendar": "^1.16.1", "axios": "^1.7.9", "bootstrap": "^5.3.6", "date-fns": "^4.1.0", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "next": "^15.1.6", "node-fetch": "^3.3.2", "prisma": "^6.7.0", "react": "^18.2.0", "react-big-calendar": "^1.17.1", "react-calendar": "^5.0.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-input-mask": "^2.0.4", "react-select": "^5.10.0", "react-toastify": "^11.0.3", "recharts": "^2.15.1", "ts-node": "^10.9.2", "zod": "^3.24.1"}, "devDependencies": {"@types/date-fns": "^2.5.3", "@types/node": "^22.15", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-input-mask": "^3.0.6", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}