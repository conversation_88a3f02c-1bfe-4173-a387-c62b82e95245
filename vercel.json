{"functions": {"src/app/api/**/*.ts": {"maxDuration": 10}}, "regions": ["fra1"], "env": {"NEXT_TELEMETRY_DISABLED": "1"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}