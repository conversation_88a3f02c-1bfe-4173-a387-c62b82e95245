# 🏥 Clinic Sacavém - Management System

A modern and complete clinic management system built with the latest web technologies.

![Next.js](https://img.shields.io/badge/Next.js-15.3.1-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Prisma](https://img.shields.io/badge/Prisma-6.7.0-2D3748?style=for-the-badge&logo=prisma)
![Supabase](https://img.shields.io/badge/Supabase-Database-3ECF8E?style=for-the-badge&logo=supabase)
![Vercel](https://img.shields.io/badge/Vercel-Deployed-000000?style=for-the-badge&logo=vercel)

## 🌟 **Live Demo**

🔗 **[View Live Demo](https://clinic-sacavem.vercel.app)**

> **Note**: This is a real production application. For demonstration purposes, please contact the developer for viewing access.

## 📋 **About the Project**

Complete management system for medical clinics that enables:
- Patient management and medical history
- Appointment scheduling and control
- Medication and inventory management
- Reports and statistics
- Responsive and intuitive interface

Specifically developed for **Clinic Sacavém**, this system digitalizes and optimizes all administrative and clinical processes.

## ⚡ **Key Features**

### 👥 **Patient Management**
- ✅ Complete patient registration
- ✅ Detailed medical history
- ✅ Advanced search by name, NIF, phone
- ✅ Contact and address data
- ✅ Medical observations

### 📅 **Appointment System**
- ✅ Interactive calendar
- ✅ Clinic or home visit bookings
- ✅ Daily and weekly views
- ✅ Schedule management
- ✅ Consultation history

### 💊 **Medication Management**
- ✅ Complete medication catalog
- ✅ Stock control
- ✅ Prices and suppliers
- ✅ Consultation associations
- ✅ Patient medication history

### 📊 **Administrative Dashboard**
- ✅ Real-time statistics
- ✅ Activity summary
- ✅ User management
- ✅ Custom reports

### 🔐 **Security and Authentication**
- ✅ Secure authentication with Clerk
- ✅ Role-based access control
- ✅ Sensitive data protection
- ✅ Automatic backup

## 🛠️ **Tech Stack**

### **Frontend**
- **Next.js 15.3.1** - React framework with SSR
- **TypeScript** - Static typing
- **Tailwind CSS** - Modern styling
- **React Calendar** - Calendar component
- **React Select** - Advanced selectors

### **Backend**
- **Next.js API Routes** - RESTful API
- **Prisma ORM** - Database management
- **PostgreSQL** - Relational database

### **Infrastructure**
- **Supabase** - Cloud database
- **Clerk** - Authentication and user management
- **Vercel** - Deployment and hosting
- **Git/GitHub** - Version control

### **Development Tools**
- **ESLint** - Code linting
- **Prettier** - Automatic formatting
- **TypeScript** - Type checking

## 🚀 **Installation and Setup**

### **Prerequisites**
- Node.js 18+
- npm or yarn
- Supabase account
- Clerk account

### **Local Setup**

1. **Clone the repository**
```bash
git clone https://github.com/DinisFigueiras/clinic.git
cd clinic
```

2. **Install dependencies**
```bash
npm install
```

3. **Configure environment variables**
```bash
cp .env.example .env
# Edit the .env file with your credentials
```

4. **Setup database**
```bash
npx prisma generate
npx prisma db push
```

5. **Run the project**
```bash
npm run dev
```

## 📱 **Screenshots**

### Main Dashboard
![Dashboard](https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=Main+Dashboard)

### Patient Management
![Patients](https://via.placeholder.com/800x400/10B981/FFFFFF?text=Patient+Management)

### Appointment Calendar
![Calendar](https://via.placeholder.com/800x400/F59E0B/FFFFFF?text=Appointment+Calendar)

## 🎯 **Technical Features**

- **Responsive Design** - Works on all devices
- **Optimized Performance** - Fast loading
- **SEO Friendly** - Search engine optimized
- **Accessibility** - Screen reader compatible
- **PWA Ready** - Can be installed as an app

## 📈 **Performance Metrics**

- ⚡ **Lighthouse Score**: 95+
- 🚀 **First Contentful Paint**: < 1.5s
- 📱 **Mobile Friendly**: 100%
- ♿ **Accessibility**: 95+

## 👨‍💻 **Developer**

**Dinis Figueiras**
- 🌐 [Portfolio](https://github.com/DinisFigueiras/Portfolio)
- 💼 [LinkedIn](https://linkedin.com/in/dinisfigueiras)
- 📧 [Email](mailto:<EMAIL>)

## 📄 **License**

This project was developed for specific use by Clinic Sacavém. All rights reserved.

---

⭐ **If you liked the project, leave a star!**