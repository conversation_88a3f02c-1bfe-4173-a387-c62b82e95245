# 🏥 Clínica Sacavém - Sistema de Gestão

Um sistema moderno e completo para gestão de clínicas médicas, desenvolvido com as mais recentes tecnologias web.

![Next.js](https://img.shields.io/badge/Next.js-15.3.1-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Prisma](https://img.shields.io/badge/Prisma-6.7.0-2D3748?style=for-the-badge&logo=prisma)
![Supabase](https://img.shields.io/badge/Supabase-Database-3ECF8E?style=for-the-badge&logo=supabase)
![Vercel](https://img.shields.io/badge/Vercel-Deployed-000000?style=for-the-badge&logo=vercel)

## 🌟 **Demonstração**

🔗 **[Ver Demo ao Vivo](https://clinic-sacavem.vercel.app)**

> **Nota**: Esta é uma aplicação real em produção. Para fins de demonstração, contacte o desenvolvedor para acesso de visualização.

## 📋 **Sobre o Projeto**

Sistema completo de gestão para clínicas médicas que permite:
- Gestão de pacientes e histórico médico
- Agendamento e controlo de consultas
- Gestão de medicamentos e stock
- Relatórios e estatísticas
- Interface responsiva e intuitiva

Desenvolvido especificamente para a **Clínica Sacavém**, este sistema digitaliza e otimiza todos os processos administrativos e clínicos.

## ⚡ **Funcionalidades Principais**

### 👥 **Gestão de Pacientes**
- ✅ Registo completo de pacientes
- ✅ Histórico médico detalhado
- ✅ Pesquisa avançada por nome, NIF, telefone
- ✅ Dados de contacto e morada
- ✅ Observações médicas

### 📅 **Sistema de Agendamentos**
- ✅ Calendário interativo
- ✅ Marcações para clínica ou domicílio
- ✅ Visualização diária e semanal
- ✅ Gestão de horários
- ✅ Histórico de consultas

### 💊 **Gestão de Medicamentos**
- ✅ Catálogo completo de medicamentos
- ✅ Controlo de stock
- ✅ Preços e fornecedores
- ✅ Associação a consultas
- ✅ Histórico de medicação por paciente

### 📊 **Dashboard Administrativo**
- ✅ Estatísticas em tempo real
- ✅ Resumo de atividade
- ✅ Gestão de utilizadores
- ✅ Relatórios personalizados

### 🔐 **Segurança e Autenticação**
- ✅ Autenticação segura com Clerk
- ✅ Controlo de acesso por roles
- ✅ Proteção de dados sensíveis
- ✅ Backup automático

## 🛠️ **Stack Tecnológico**

### **Frontend**
- **Next.js 15.3.1** - Framework React com SSR
- **TypeScript** - Tipagem estática
- **Tailwind CSS** - Estilização moderna
- **React Calendar** - Componente de calendário
- **React Select** - Seletores avançados

### **Backend**
- **Next.js API Routes** - API RESTful
- **Prisma ORM** - Gestão de base de dados
- **PostgreSQL** - Base de dados relacional

### **Infraestrutura**
- **Supabase** - Base de dados na cloud
- **Clerk** - Autenticação e gestão de utilizadores
- **Vercel** - Deploy e hosting
- **Git/GitHub** - Controlo de versão

### **Ferramentas de Desenvolvimento**
- **ESLint** - Linting de código
- **Prettier** - Formatação automática
- **TypeScript** - Verificação de tipos

## 🚀 **Instalação e Configuração**

### **Pré-requisitos**
- Node.js 18+
- npm ou yarn
- Conta Supabase
- Conta Clerk

### **Configuração Local**

1. **Clone o repositório**
```bash
git clone https://github.com/DinisFigueiras/clinic.git
cd clinic
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure as variáveis de ambiente**
```bash
cp .env.example .env
# Edite o ficheiro .env com as suas credenciais
```

4. **Configure a base de dados**
```bash
npx prisma generate
npx prisma db push
```

5. **Execute o projeto**
```bash
npm run dev
```

## 📱 **Screenshots**

### Dashboard Principal
![Dashboard](https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=Dashboard+Principal)

### Gestão de Pacientes
![Pacientes](https://via.placeholder.com/800x400/10B981/FFFFFF?text=Gestão+de+Pacientes)

### Calendário de Consultas
![Calendário](https://via.placeholder.com/800x400/F59E0B/FFFFFF?text=Calendário+de+Consultas)

## 🎯 **Características Técnicas**

- **Responsive Design** - Funciona em todos os dispositivos
- **Performance Otimizada** - Carregamento rápido
- **SEO Friendly** - Otimizado para motores de busca
- **Acessibilidade** - Compatível com leitores de ecrã
- **PWA Ready** - Pode ser instalado como app

## 📈 **Métricas de Performance**

- ⚡ **Lighthouse Score**: 95+
- 🚀 **First Contentful Paint**: < 1.5s
- 📱 **Mobile Friendly**: 100%
- ♿ **Accessibility**: 95+

## 👨‍💻 **Desenvolvedor**

**Dinis Figueiras**
- 🌐 [Portfolio](https://github.com/DinisFigueiras/Portfolio)
- 💼 [LinkedIn](https://linkedin.com/in/dinisfigueiras)
- 📧 [Email](mailto:<EMAIL>)

## 📄 **Licença**

Este projeto foi desenvolvido para uso específico da Clínica Sacavém. Todos os direitos reservados.

---

⭐ **Se gostou do projeto, deixe uma estrela!**